#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test spécifique d'une région de mise
Usage: python test_region_specifique.py <numero_joueur>
"""

import sys
import cv2
import numpy as np
from detector import Detector
import mss
import json

def test_region_specifique(joueur_num):
    """Test une région mise_joueur spécifique"""
    print(f"🎯 TEST RÉGION MISE_JOUEUR{joueur_num}")
    print("=" * 40)
    
    # Créer détecteur
    detector = Detector()
    
    # Capture d'écran
    with mss.mss() as sct:
        screenshot = sct.grab(sct.monitors[1])
        screenshot_np = np.array(screenshot)
        screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_BGRA2BGR)
    
    # Charger config
    config_path = r"C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    region_name = f"mise_joueur{joueur_num}"
    if region_name not in config['all_regions']:
        print(f"❌ Région {region_name} non trouvée")
        return
    
    region_data = config['all_regions'][region_name]
    x, y, w, h = region_data['x'], region_data['y'], region_data['width'], region_data['height']
    
    # Extraire région
    region_img = screenshot_bgr[y:y+h, x:x+w]
    
    print(f"📍 Position: ({x}, {y}) - Taille: {w}x{h}")
    
    # Tests multiples
    methods = [
        ("OCR Direct", lambda img: detector.detect_text_simple(img)),
        ("Montant Spécialisé", lambda img: detector.detect_amount_text(img)),
        ("Multi-OCR", lambda img: detector.detect_text_multi_ocr(img, False, False))
    ]
    
    for method_name, method_func in methods:
        try:
            result = method_func(region_img)
            print(f"📝 {method_name}: '{result}'")
        except Exception as e:
            print(f"❌ {method_name}: Erreur - {e}")
    
    # Sauvegarder pour inspection
    cv2.imwrite(f"test_mise_joueur{joueur_num}.jpg", region_img)
    print(f"💾 Image sauvée: test_mise_joueur{joueur_num}.jpg")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python test_region_specifique.py <numero_joueur>")
        sys.exit(1)
    
    try:
        joueur_num = int(sys.argv[1])
        test_region_specifique(joueur_num)
    except ValueError:
        print("❌ Numéro de joueur invalide")
