#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug en temps réel de la détection des mises
Vérifie étape par étape pourquoi les mises ne sont pas détectées
"""

import sys
import os
import json
from pathlib import Path

def verifier_configuration_mises():
    """Vérifie la configuration des régions de mises"""
    print("🔧 VÉRIFICATION CONFIGURATION DES MISES")
    print("=" * 50)
    
    config_path = r"C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json"
    
    if not os.path.exists(config_path):
        print("❌ Fichier de configuration non trouvé !")
        print(f"   Chemin: {config_path}")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        all_regions = config.get('all_regions', {})
        
        # Chercher toutes les régions de mises
        mise_regions = {}
        for region_name, region_data in all_regions.items():
            if 'mise_joueur' in region_name:
                mise_regions[region_name] = region_data
        
        print(f"📊 Régions de mises trouvées: {len(mise_regions)}")
        print("-" * 40)
        
        if not mise_regions:
            print("❌ AUCUNE RÉGION MISE_JOUEUR TROUVÉE !")
            print("   Vous devez calibrer les régions mise_joueur1 à mise_joueur6")
            return False
        
        for region_name, region_data in mise_regions.items():
            x = region_data.get('x', 0)
            y = region_data.get('y', 0)
            width = region_data.get('width', 0)
            height = region_data.get('height', 0)
            
            print(f"✅ {region_name}: ({x}, {y}) - {width}x{height}")
            
            # Vérifier si la région est valide
            if width < 10 or height < 10:
                print(f"   ⚠️ Région très petite, peut causer des problèmes")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lecture configuration: {e}")
        return False

def verifier_detection_dans_detector():
    """Vérifie si la détection est activée dans detector.py"""
    print("\n🔍 VÉRIFICATION DETECTOR.PY")
    print("=" * 50)
    
    try:
        with open("detector.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Vérifier les éléments critiques
        checks = [
            ("mise_joueur", "Traitement des régions mise_joueur"),
            ("detect_colors_fast", "Détection des couleurs"),
            ("💲 MISE JOUEUR", "Messages de debug pour mises"),
            ("if text and len(text) >= 1:", "Vérification texte détecté"),
            ("colors = self.detect_colors_fast", "Appel détection couleurs")
        ]
        
        print("🔍 Éléments critiques :")
        print("-" * 40)
        
        missing_elements = []
        for check, description in checks:
            if check in content:
                print(f"✅ {description}")
            else:
                print(f"❌ MANQUANT: {description}")
                missing_elements.append(check)
        
        if missing_elements:
            print(f"\n⚠️ {len(missing_elements)} éléments manquants détectés")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def verifier_ocr_configuration():
    """Vérifie la configuration OCR"""
    print("\n👁️ VÉRIFICATION CONFIGURATION OCR")
    print("=" * 50)
    
    try:
        # Vérifier si PaddleOCR est configuré
        with open("detector.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        if "PaddleOCR" in content:
            print("✅ PaddleOCR détecté")
            
            if "use_gpu=True" in content:
                print("✅ GPU activé pour OCR")
            else:
                print("⚠️ GPU non activé pour OCR")
            
            if "lang='en'" in content or "lang='fr'" in content:
                print("✅ Langue configurée")
            else:
                print("⚠️ Langue non spécifiée")
        
        elif "easyocr" in content.lower():
            print("✅ EasyOCR détecté")
        else:
            print("❌ Aucun OCR détecté")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def creer_script_test_mises():
    """Crée un script de test spécifique pour les mises"""
    print("\n🧪 CRÉATION SCRIPT DE TEST")
    print("=" * 50)
    
    test_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from detector import Detector

def test_mises_specifique():
    print("🎯 TEST SPÉCIFIQUE DES MISES")
    print("=" * 40)
    
    # Créer le détecteur
    detector = Detector()
    
    # Charger la configuration
    config_path = r"C:\\Users\\<USER>\\PokerAdvisor\\Calibration\\config\\poker_advisor_config.json"
    if not detector.load_config(config_path):
        print("❌ Impossible de charger la configuration")
        return False
    
    print("✅ Configuration chargée")
    
    # Faire une capture d'écran
    print("📸 Capture d'écran en cours...")
    screenshot = detector.capture_screenshot()
    if screenshot is None:
        print("❌ Impossible de capturer l'écran")
        return False
    
    print("✅ Capture d'écran réussie")
    
    # Tester chaque région de mise
    for i in range(1, 7):
        region_name = f"mise_joueur{i}"
        print(f"\\n🔍 Test {region_name}:")
        
        if region_name in detector.regions:
            region = detector.regions[region_name]
            x, y, w, h = region['x'], region['y'], region['width'], region['height']
            
            # Extraire la région
            region_img = screenshot[y:y+h, x:x+w]
            
            # Détecter le texte
            text = detector.detect_text_in_region(region_img)
            print(f"   Texte détecté: '{text}'")
            
            # Détecter les couleurs
            colors = detector.detect_colors_fast(region_img)
            print(f"   Couleurs détectées: {colors}")
            
            if text and text.strip():
                print(f"   ✅ Mise détectée: {text}")
            else:
                print(f"   ❌ Aucune mise détectée")
        else:
            print(f"   ❌ Région {region_name} non configurée")
    
    return True

if __name__ == "__main__":
    test_mises_specifique()
"""
    
    # Sauvegarder le script
    with open("test_mises_debug.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("✅ Script de test créé: test_mises_debug.py")
    print("🚀 Exécutez: python test_mises_debug.py")
    
    return True

def verifier_logs_existants():
    """Vérifie les logs existants pour des indices"""
    print("\n📝 VÉRIFICATION LOGS EXISTANTS")
    print("=" * 50)
    
    # Chercher des fichiers de logs
    log_paths = [
        "detector.log",
        "poker_advisor.log",
        "debug.log",
        "../detector.log",
        "../poker_advisor.log"
    ]
    
    logs_found = []
    for log_path in log_paths:
        if os.path.exists(log_path):
            logs_found.append(log_path)
    
    if not logs_found:
        print("⚠️ Aucun fichier de log trouvé")
        print("   Les logs apparaissent probablement dans la console")
        return False
    
    print(f"📁 Fichiers de log trouvés: {len(logs_found)}")
    
    for log_path in logs_found:
        print(f"\n📄 Analyse de {log_path}:")
        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Chercher des mentions de mises
            if "mise_joueur" in content:
                print("   ✅ Mentions de mise_joueur trouvées")
            else:
                print("   ❌ Aucune mention de mise_joueur")
            
            if "💲" in content:
                print("   ✅ Messages de debug mises trouvés")
            else:
                print("   ❌ Aucun message de debug mises")
                
        except Exception as e:
            print(f"   ❌ Erreur lecture: {e}")
    
    return True

def diagnostic_complet():
    """Effectue un diagnostic complet"""
    print("🔍 DIAGNOSTIC COMPLET - MISES NON DÉTECTÉES")
    print("=" * 60)
    print("Recherche de la cause du problème...")
    print()
    
    # Effectuer toutes les vérifications
    verifications = [
        ("Configuration", verifier_configuration_mises()),
        ("Detector.py", verifier_detection_dans_detector()),
        ("OCR", verifier_ocr_configuration()),
        ("Script test", creer_script_test_mises()),
        ("Logs", verifier_logs_existants())
    ]
    
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DU DIAGNOSTIC")
    print("-" * 60)
    
    problemes = []
    for nom, resultat in verifications:
        if resultat:
            print(f"✅ {nom}: OK")
        else:
            print(f"❌ {nom}: PROBLÈME")
            problemes.append(nom)
    
    if problemes:
        print(f"\n🚨 PROBLÈMES IDENTIFIÉS: {', '.join(problemes)}")
        
        print("\n🔧 SOLUTIONS RECOMMANDÉES:")
        print("-" * 60)
        
        if "Configuration" in problemes:
            print("1. **Calibrer les régions mise_joueur**")
            print("   - Ouvrez calibration_simple.py")
            print("   - Ajoutez les régions mise_joueur1 à mise_joueur6")
            print("   - Placez-les sur les montants des mises")
        
        if "Detector.py" in problemes:
            print("2. **Vérifier detector.py**")
            print("   - Le code de détection des mises est manquant")
            print("   - Réinstaller ou corriger le fichier")
        
        if "OCR" in problemes:
            print("3. **Configurer l'OCR**")
            print("   - Vérifier que PaddleOCR est installé")
            print("   - Activer le GPU si disponible")
        
    else:
        print("\n✅ CONFIGURATION SEMBLE CORRECTE")
        print("\n🔍 ÉTAPES DE DEBUG SUIVANTES:")
        print("-" * 60)
        print("1. Exécutez: python test_mises_debug.py")
        print("2. Observez les messages dans la console")
        print("3. Vérifiez que les régions couvrent bien les montants")
        print("4. Testez avec des montants simples (1, 5, 10)")
    
    print("\n🚀 POUR CONTINUER LE DEBUG:")
    print("-" * 60)
    print("1. Lancez 'lancer_detector_cuda_advisor.bat'")
    print("2. Activez le conseiller poker")
    print("3. Cherchez dans la console les messages '💲 MISE JOUEUR'")
    print("4. Si aucun message, le problème est dans la calibration")
    print("5. Si messages présents, le problème est dans le traitement")

if __name__ == "__main__":
    diagnostic_complet()
