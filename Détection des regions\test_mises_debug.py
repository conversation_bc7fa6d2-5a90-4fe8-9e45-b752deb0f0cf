#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from detector import Detector

def test_mises_specifique():
    print("🎯 TEST SPÉCIFIQUE DES MISES")
    print("=" * 40)
    
    # Créer le détecteur
    detector = Detector()
    
    # Charger la configuration
    config_path = r"C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json"
    if not detector.load_config(config_path):
        print("❌ Impossible de charger la configuration")
        return False
    
    print("✅ Configuration chargée")
    
    # Faire une capture d'écran
    print("📸 Capture d'écran en cours...")
    screenshot = detector.capture_screenshot()
    if screenshot is None:
        print("❌ Impossible de capturer l'écran")
        return False
    
    print("✅ Capture d'écran réussie")
    
    # Tester chaque région de mise
    for i in range(1, 7):
        region_name = f"mise_joueur{i}"
        print(f"\n🔍 Test {region_name}:")
        
        if region_name in detector.regions:
            region = detector.regions[region_name]
            x, y, w, h = region['x'], region['y'], region['width'], region['height']
            
            # Extraire la région
            region_img = screenshot[y:y+h, x:x+w]
            
            # Détecter le texte
            text = detector.detect_text_in_region(region_img)
            print(f"   Texte détecté: '{text}'")
            
            # Détecter les couleurs
            colors = detector.detect_colors_fast(region_img)
            print(f"   Couleurs détectées: {colors}")
            
            if text and text.strip():
                print(f"   ✅ Mise détectée: {text}")
            else:
                print(f"   ❌ Aucune mise détectée")
        else:
            print(f"   ❌ Région {region_name} non configurée")
    
    return True

if __name__ == "__main__":
    test_mises_specifique()
