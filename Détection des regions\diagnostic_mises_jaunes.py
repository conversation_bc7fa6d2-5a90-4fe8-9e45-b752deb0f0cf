#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnostic spécialisé pour les mises jaunes/oranges
Analyse pourquoi seules certaines mises jaunes sont détectées
"""

import sys
import os
import json
import cv2
import numpy as np

def analyser_detection_couleur_jaune():
    """Analyse spécifique de la détection des couleurs jaunes/oranges"""
    print("🟡 DIAGNOSTIC SPÉCIALISÉ - MISES JAUNES/ORANGES")
    print("=" * 60)
    
    try:
        from detector import Detector
        
        # Créer le détecteur
        detector = Detector()
        print("✅ Détecteur créé")
        
        # Capture d'écran
        import mss
        with mss.mss() as sct:
            screenshot = sct.grab(sct.monitors[1])
            screenshot_np = np.array(screenshot)
            screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_BGRA2BGR)
        
        print(f"✅ Capture réussie: {screenshot_bgr.shape}")
        
        # Charger la configuration
        config_path = r"C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        all_regions = config.get('all_regions', {})
        
        print("\n🔍 ANALYSE DES COULEURS JAUNES/ORANGES PAR RÉGION")
        print("-" * 60)
        
        mises_detectees = []
        mises_non_detectees = []
        
        # Analyser chaque région mise_joueur
        for i in range(1, 8):
            region_name = f"mise_joueur{i}"
            
            if region_name in all_regions:
                print(f"\n🎯 {region_name.upper()}")
                print("-" * 30)
                
                region_data = all_regions[region_name]
                x, y, w, h = region_data['x'], region_data['y'], region_data['width'], region_data['height']
                
                # Extraire la région
                region_img = screenshot_bgr[y:y+h, x:x+w]
                
                # Analyser les couleurs avec différentes méthodes
                print(f"📍 Position: ({x}, {y}) - Taille: {w}x{h}")
                
                # Méthode 1: Détection couleurs rapide (actuelle)
                colors_fast = detector.detect_colors_fast(region_img)
                print(f"🎨 Couleurs détectées (rapide): {colors_fast}")
                
                # Méthode 2: Analyse HSV spécialisée pour jaune/orange
                colors_hsv = analyser_couleurs_hsv_jaune_orange(region_img)
                print(f"🟡 Analyse HSV jaune/orange: {colors_hsv}")
                
                # Méthode 3: Analyse RGB spécialisée
                colors_rgb = analyser_couleurs_rgb_jaune_orange(region_img)
                print(f"🟠 Analyse RGB jaune/orange: {colors_rgb}")
                
                # Détection de texte
                try:
                    text = detector.detect_amount_text(region_img)
                    print(f"📝 Texte détecté: '{text}'")
                    
                    if text and text.strip():
                        mises_detectees.append({
                            'region': region_name,
                            'text': text,
                            'colors_fast': colors_fast,
                            'colors_hsv': colors_hsv,
                            'colors_rgb': colors_rgb,
                            'position': (x, y, w, h)
                        })
                        print(f"✅ MISE DÉTECTÉE: {text}")
                    else:
                        mises_non_detectees.append({
                            'region': region_name,
                            'colors_fast': colors_fast,
                            'colors_hsv': colors_hsv,
                            'colors_rgb': colors_rgb,
                            'position': (x, y, w, h)
                        })
                        print(f"❌ AUCUNE MISE DÉTECTÉE")
                        
                except Exception as e:
                    print(f"❌ Erreur détection texte: {e}")
                    mises_non_detectees.append({
                        'region': region_name,
                        'error': str(e),
                        'colors_fast': colors_fast,
                        'colors_hsv': colors_hsv,
                        'colors_rgb': colors_rgb,
                        'position': (x, y, w, h)
                    })
                
                # Sauvegarder l'image pour inspection
                cv2.imwrite(f"debug_jaune_{region_name}.jpg", region_img)
                
                # Créer une version avec masque jaune/orange pour visualisation
                mask_jaune_orange = creer_masque_jaune_orange(region_img)
                cv2.imwrite(f"debug_jaune_{region_name}_mask.jpg", mask_jaune_orange)
                print(f"💾 Images sauvées: debug_jaune_{region_name}.jpg et debug_jaune_{region_name}_mask.jpg")
        
        # Analyser les résultats
        analyser_resultats_detection(mises_detectees, mises_non_detectees)
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyser_couleurs_hsv_jaune_orange(image):
    """Analyse HSV spécialisée pour détecter jaune et orange"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # Plages HSV pour jaune et orange
    # Jaune: H=20-30, S=100-255, V=100-255
    # Orange: H=10-20, S=100-255, V=100-255
    
    # Masque pour jaune
    lower_yellow = np.array([20, 100, 100])
    upper_yellow = np.array([30, 255, 255])
    mask_yellow = cv2.inRange(hsv, lower_yellow, upper_yellow)
    
    # Masque pour orange
    lower_orange = np.array([10, 100, 100])
    upper_orange = np.array([20, 255, 255])
    mask_orange = cv2.inRange(hsv, lower_orange, upper_orange)
    
    # Calculer les pourcentages
    total_pixels = image.shape[0] * image.shape[1]
    yellow_pixels = np.sum(mask_yellow > 0)
    orange_pixels = np.sum(mask_orange > 0)
    
    yellow_percent = (yellow_pixels / total_pixels) * 100
    orange_percent = (orange_pixels / total_pixels) * 100
    
    colors = []
    if yellow_percent > 5:  # Seuil de 5%
        colors.append(f'yellow({yellow_percent:.1f}%)')
    if orange_percent > 5:
        colors.append(f'orange({orange_percent:.1f}%)')
    
    return colors if colors else ['none']

def analyser_couleurs_rgb_jaune_orange(image):
    """Analyse RGB spécialisée pour détecter jaune et orange"""
    # Convertir en RGB
    rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Masques RGB pour jaune et orange
    # Jaune: R>200, G>200, B<100
    # Orange: R>200, G>100-200, B<100
    
    # Masque jaune
    mask_yellow = (rgb[:,:,0] > 200) & (rgb[:,:,1] > 200) & (rgb[:,:,2] < 100)
    
    # Masque orange
    mask_orange = (rgb[:,:,0] > 200) & (rgb[:,:,1] > 100) & (rgb[:,:,1] < 200) & (rgb[:,:,2] < 100)
    
    # Calculer les pourcentages
    total_pixels = image.shape[0] * image.shape[1]
    yellow_pixels = np.sum(mask_yellow)
    orange_pixels = np.sum(mask_orange)
    
    yellow_percent = (yellow_pixels / total_pixels) * 100
    orange_percent = (orange_pixels / total_pixels) * 100
    
    colors = []
    if yellow_percent > 5:
        colors.append(f'yellow_rgb({yellow_percent:.1f}%)')
    if orange_percent > 5:
        colors.append(f'orange_rgb({orange_percent:.1f}%)')
    
    return colors if colors else ['none']

def creer_masque_jaune_orange(image):
    """Crée un masque visuel pour les couleurs jaunes/oranges"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # Masques combinés pour jaune et orange
    lower_yellow = np.array([20, 100, 100])
    upper_yellow = np.array([30, 255, 255])
    mask_yellow = cv2.inRange(hsv, lower_yellow, upper_yellow)
    
    lower_orange = np.array([10, 100, 100])
    upper_orange = np.array([20, 255, 255])
    mask_orange = cv2.inRange(hsv, lower_orange, upper_orange)
    
    # Combiner les masques
    mask_combined = cv2.bitwise_or(mask_yellow, mask_orange)
    
    # Créer une image colorée du masque
    mask_colored = cv2.applyColorMap(mask_combined, cv2.COLORMAP_HOT)
    
    return mask_colored

def analyser_resultats_detection(mises_detectees, mises_non_detectees):
    """Analyse les résultats pour identifier les patterns"""
    print(f"\n📊 ANALYSE DES RÉSULTATS")
    print("=" * 60)
    
    print(f"✅ Mises détectées: {len(mises_detectees)}")
    print(f"❌ Mises non détectées: {len(mises_non_detectees)}")
    
    if mises_detectees:
        print(f"\n🎯 MISES DÉTECTÉES AVEC SUCCÈS:")
        print("-" * 40)
        for mise in mises_detectees:
            print(f"   {mise['region']}: '{mise['text']}'")
            print(f"      Couleurs rapide: {mise['colors_fast']}")
            print(f"      Couleurs HSV: {mise['colors_hsv']}")
            print(f"      Couleurs RGB: {mise['colors_rgb']}")
            print(f"      Position: {mise['position']}")
            print()
    
    if mises_non_detectees:
        print(f"\n❌ MISES NON DÉTECTÉES:")
        print("-" * 40)
        for mise in mises_non_detectees:
            print(f"   {mise['region']}:")
            print(f"      Couleurs rapide: {mise['colors_fast']}")
            print(f"      Couleurs HSV: {mise['colors_hsv']}")
            print(f"      Couleurs RGB: {mise['colors_rgb']}")
            print(f"      Position: {mise['position']}")
            if 'error' in mise:
                print(f"      Erreur: {mise['error']}")
            print()
    
    # Identifier les patterns
    print(f"\n🔍 ANALYSE DES PATTERNS:")
    print("-" * 40)
    
    if mises_detectees:
        # Analyser les couleurs des mises détectées
        colors_detectees = []
        for mise in mises_detectees:
            colors_detectees.extend(mise['colors_fast'])
            colors_detectees.extend(mise['colors_hsv'])
            colors_detectees.extend(mise['colors_rgb'])
        
        print(f"Couleurs présentes dans les mises détectées: {set(colors_detectees)}")
    
    if mises_non_detectees:
        # Analyser les couleurs des mises non détectées
        colors_non_detectees = []
        for mise in mises_non_detectees:
            colors_non_detectees.extend(mise['colors_fast'])
            colors_non_detectees.extend(mise['colors_hsv'])
            colors_non_detectees.extend(mise['colors_rgb'])
        
        print(f"Couleurs présentes dans les mises NON détectées: {set(colors_non_detectees)}")

def recommandations_amelioration_jaune():
    """Recommandations spécifiques pour améliorer la détection des mises jaunes"""
    print(f"\n🔧 RECOMMANDATIONS SPÉCIFIQUES - MISES JAUNES/ORANGES")
    print("=" * 60)
    
    print("1. **Vérifier les images de debug**")
    print("   - Examinez debug_jaune_mise_joueur*.jpg")
    print("   - Observez debug_jaune_mise_joueur*_mask.jpg (masques couleurs)")
    print("   - Vérifiez que les régions couvrent bien le texte jaune/orange")
    
    print("\n2. **Ajuster la détection de couleurs**")
    print("   - Les mises jaunes peuvent être détectées comme 'orange' ou 'green'")
    print("   - Vérifiez les seuils HSV pour jaune (H=20-30)")
    print("   - Ajustez les seuils RGB si nécessaire")
    
    print("\n3. **Calibration spécialisée**")
    print("   - Positionnez les régions exactement sur le texte jaune")
    print("   - Évitez les bordures et arrière-plans")
    print("   - Testez avec différentes tailles de région")
    
    print("\n4. **Améliorer le prétraitement OCR**")
    print("   - Le texte jaune sur fond sombre nécessite un prétraitement spécial")
    print("   - Augmenter le contraste avant OCR")
    print("   - Utiliser la binarisation adaptative")
    
    print("\n5. **Tests spécifiques**")
    print("   - Testez sur une table avec plusieurs mises jaunes visibles")
    print("   - Comparez les régions qui fonctionnent vs celles qui échouent")
    print("   - Ajustez progressivement les paramètres")

def main():
    """Fonction principale"""
    print("🟡 DIAGNOSTIC SPÉCIALISÉ - MISES JAUNES/ORANGES")
    print("=" * 70)
    print("Analyse pourquoi seules certaines mises jaunes sont détectées")
    print()
    
    # Effectuer l'analyse
    success = analyser_detection_couleur_jaune()
    
    # Recommandations
    recommandations_amelioration_jaune()
    
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ DU DIAGNOSTIC SPÉCIALISÉ")
    print("-" * 70)
    
    if success:
        print("✅ Analyse des couleurs jaunes/oranges terminée")
        print("📁 Images de debug créées avec masques couleurs")
        print("🔍 Patterns de détection identifiés")
        
        print("\n🚀 ÉTAPES SUIVANTES :")
        print("1. Examinez les images debug_jaune_*.jpg")
        print("2. Observez les masques *_mask.jpg pour voir les zones jaunes détectées")
        print("3. Comparez les régions qui fonctionnent vs celles qui échouent")
        print("4. Ajustez la calibration des régions problématiques")
        print("5. Testez avec python test_region_specifique.py <numero>")
    else:
        print("❌ Erreur lors de l'analyse")
        print("Vérifiez que vous êtes sur une table avec des mises jaunes visibles")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
