#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'interface agrandie avec couleurs améliorées
Vérifie que la fenêtre et les écritures sont plus grandes
"""

def test_tailles_interface():
    """Test des nouvelles tailles de l'interface"""
    print("📏 TEST DES TAILLES DE L'INTERFACE")
    print("=" * 50)
    
    # Vérifier les modifications dans detector_gui.py
    try:
        with open("detector_gui.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Vérifications des tailles
        checks = [
            ("setMinimumSize(1200, 900)", "✅ Taille minimale fenêtre augmentée"),
            ("resize(1600, 1000)", "✅ Taille par défaut fenêtre augmentée"),
            ("setMinimumHeight(500)", "✅ Hauteur zone d'analyse augmentée"),
            ("setFont(QFont(\"Consolas\", 12))", "✅ Police zone d'analyse agrandie"),
            ("font-size: 12px", "✅ Taille de police CSS augmentée"),
            ("line-height: 1.5", "✅ Espacement des lignes amélioré"),
            ("padding: 15px", "✅ Espacement interne augmenté"),
            ("border: 2px", "✅ Bordures plus épaisses")
        ]
        
        print("🔍 Vérification des modifications :")
        print("-" * 40)
        
        for check, message in checks:
            if check in content:
                print(message)
            else:
                print(f"❌ MANQUANT: {check}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

def test_couleurs_ameliorees():
    """Test des couleurs améliorées"""
    print("\n🎨 TEST DES COULEURS AMÉLIORÉES")
    print("=" * 50)
    
    # Simuler la méthode add_card_colors_to_analysis avec les nouvelles couleurs
    def test_colorization():
        # Nouvelles couleurs plus vives
        new_colors = {
            "cœur": "#FF3333",    # Rouge vif (au lieu de #FF0000)
            "pique": "#FFFFFF",   # Blanc avec ombre (au lieu de #000000)
            "carreau": "#3399FF", # Bleu vif (au lieu de #0066FF)
            "trèfle": "#33CC33"   # Vert vif (au lieu de #00AA00)
        }
        
        print("🎨 Nouvelles couleurs implémentées :")
        print("-" * 40)
        for suit, color in new_colors.items():
            print(f"   {suit}: {color} (plus vif)")
        
        # Nouvelles tailles de police
        print("\n📏 Nouvelles tailles de police :")
        print("-" * 40)
        print("   Symboles (♥♠♦♣): 16px (au lieu de taille normale)")
        print("   Texte couleurs: 14px (au lieu de taille normale)")
        print("   Cartes format 'X de Y': 14px")
        print("   Sections importantes: 16px")
        print("   Montants BB: 14px")
        
        # Améliorations visuelles
        print("\n✨ Améliorations visuelles :")
        print("-" * 40)
        print("   ✅ Pique avec ombre pour visibilité sur fond noir")
        print("   ✅ Sections avec arrière-plan coloré")
        print("   ✅ Montants en jaune vif")
        print("   ✅ Actions recommandées en vert vif")
        print("   ✅ Titres en orange vif")
        
        return True
    
    return test_colorization()

def test_exemple_analyse():
    """Test avec un exemple d'analyse colorée"""
    print("\n📊 EXEMPLE D'ANALYSE COLORÉE")
    print("=" * 50)
    
    # Exemple d'analyse avant colorisation
    exemple_avant = """
### Situation (6 régions détectées, 1 corrections manuelles)
Main 1 : A de cœur, K de pique

📊 Analyse détaillée:
• Main actuelle : As haut
• Cartes en main: A de cœur, K de pique
• Cartes sur le board: Q de carreau, J de trèfle, 10 de cœur
• Équité estimée vs range (70-85%) : ~77.5%
• Pot odds : 25.0%   Cote implicite : Favorable
• Action recommandée : RAISE 45.0 BB
• Montant recommandé : 45.0 BB (relance)
    """
    
    print("📝 AVANT (texte normal) :")
    print("-" * 40)
    print(exemple_avant.strip())
    
    print("\n🎨 APRÈS (avec couleurs et tailles) :")
    print("-" * 40)
    print("📊 Analyse détaillée: (bleu vif, 16px, arrière-plan)")
    print("• Main actuelle : As haut")
    print("• Cartes en main: (orange vif, 14px) A♥ (rouge vif, 16px), K♠ (blanc avec ombre, 16px)")
    print("• Cartes sur le board: (orange vif, 14px) Q♦ (bleu vif, 16px), J♣ (vert vif, 16px), 10♥ (rouge vif, 16px)")
    print("• Équité estimée vs range (70-85%) : ~77.5%")
    print("• Pot odds : 25.0%   Cote implicite : Favorable")
    print("• Action recommandée: (vert vif, 16px, arrière-plan) RAISE 45.0 BB (jaune vif, 14px)")
    print("• Montant recommandé : 45.0 BB (jaune vif, 14px) (relance)")

def test_dimensions_fenetres():
    """Test des nouvelles dimensions"""
    print("\n📐 NOUVELLES DIMENSIONS")
    print("=" * 50)
    
    dimensions = {
        "Fenêtre principale": {
            "Avant": "1000x700 minimum",
            "Après": "1200x900 minimum, 1600x1000 par défaut"
        },
        "Zone d'analyse": {
            "Avant": "120px maximum",
            "Après": "500px minimum"
        },
        "Police principale": {
            "Avant": "Consolas 10px",
            "Après": "Consolas 12px"
        },
        "Symboles cartes": {
            "Avant": "Taille normale",
            "Après": "16px"
        },
        "Texte couleurs": {
            "Avant": "Taille normale",
            "Après": "14px"
        }
    }
    
    print("📊 Comparaison des dimensions :")
    print("-" * 40)
    
    for element, sizes in dimensions.items():
        print(f"\n{element}:")
        print(f"   Avant: {sizes['Avant']}")
        print(f"   Après: {sizes['Après']}")

def main():
    """Fonction principale de test"""
    print("📏 TEST COMPLET DE L'INTERFACE AGRANDIE")
    print("=" * 60)
    print("Vérification que la fenêtre et les écritures sont plus grandes")
    print()
    
    # Tests
    tests = [
        test_tailles_interface(),
        test_couleurs_ameliorees(),
    ]
    
    # Exemples et dimensions
    test_exemple_analyse()
    test_dimensions_fenetres()
    
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES AMÉLIORATIONS")
    print("-" * 60)
    print("✅ Fenêtre principale agrandie :")
    print("   • Minimum: 1200x900 (au lieu de 1000x700)")
    print("   • Par défaut: 1600x1000")
    print()
    print("✅ Zone d'analyse agrandie :")
    print("   • Hauteur: 500px minimum (au lieu de 120px max)")
    print("   • Police: Consolas 12px (au lieu de 10px)")
    print("   • Espacement: 15px padding, line-height 1.5")
    print()
    print("✅ Couleurs plus vives :")
    print("   • Rouge: #FF3333 (plus vif)")
    print("   • Pique: #FFFFFF avec ombre (visible sur fond noir)")
    print("   • Bleu: #3399FF (plus vif)")
    print("   • Vert: #33CC33 (plus vif)")
    print()
    print("✅ Tailles de police augmentées :")
    print("   • Symboles cartes: 16px")
    print("   • Texte couleurs: 14px")
    print("   • Sections importantes: 16px")
    print("   • Montants: 14px")
    print()
    print("🚀 PRÊT À UTILISER :")
    print("   1. Lancez 'lancer_detector_cuda_advisor.bat'")
    print("   2. Observez la fenêtre plus grande")
    print("   3. Testez l'analyse avec cartes colorées et plus lisibles")
    
    success_count = sum(tests)
    total_count = len(tests)
    
    if success_count == total_count:
        print(f"\n✅ TOUS LES TESTS RÉUSSIS ({success_count}/{total_count})")
    else:
        print(f"\n⚠️ {success_count}/{total_count} tests réussis")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
