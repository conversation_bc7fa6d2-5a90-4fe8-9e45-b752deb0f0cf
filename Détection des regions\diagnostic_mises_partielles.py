#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnostic pour les mises partiellement détectées
Analyse pourquoi certaines mises ne sont pas détectées
"""

import sys
import os
import json
import cv2
import numpy as np

def analyser_regions_mises():
    """Analyse détaillée des régions de mises"""
    print("🔍 ANALYSE DÉTAILLÉE DES RÉGIONS DE MISES")
    print("=" * 50)
    
    try:
        # Importer le détecteur
        from detector import Detector
        
        # Créer le détecteur
        detector = Detector()
        print("✅ Détecteur créé")
        
        # Faire une capture d'écran
        print("📸 Capture d'écran en cours...")
        
        import mss
        with mss.mss() as sct:
            screenshot = sct.grab(sct.monitors[1])
            import numpy as np
            screenshot_np = np.array(screenshot)
            screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_BGRA2BGR)
            
            print(f"✅ Capture réussie: {screenshot_bgr.shape}")
            
            # Analyser chaque région de mise individuellement
            config_path = r"C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json"
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            all_regions = config.get('all_regions', {})
            
            # Analyser chaque région mise_joueur
            for i in range(1, 8):
                region_name = f"mise_joueur{i}"
                
                if region_name in all_regions:
                    print(f"\n🎯 ANALYSE {region_name.upper()}")
                    print("-" * 40)
                    
                    region_data = all_regions[region_name]
                    x = region_data['x']
                    y = region_data['y']
                    width = region_data['width']
                    height = region_data['height']
                    
                    print(f"📍 Position: ({x}, {y}) - Taille: {width}x{height}")
                    
                    # Extraire la région
                    region_img = screenshot_bgr[y:y+height, x:x+width]
                    
                    # Sauvegarder l'image de la région pour inspection
                    region_filename = f"debug_mise_joueur{i}.jpg"
                    cv2.imwrite(region_filename, region_img)
                    print(f"💾 Image sauvée: {region_filename}")
                    
                    # Analyser les couleurs
                    colors = detector.detect_colors_fast(region_img)
                    print(f"🎨 Couleurs détectées: {colors}")
                    
                    # Analyser la luminosité
                    gray = cv2.cvtColor(region_img, cv2.COLOR_BGR2GRAY)
                    mean_brightness = np.mean(gray)
                    print(f"💡 Luminosité moyenne: {mean_brightness:.1f}")
                    
                    # Analyser le contraste
                    contrast = np.std(gray)
                    print(f"📊 Contraste: {contrast:.1f}")
                    
                    # Tester différentes méthodes OCR
                    print("🔍 Tests OCR :")
                    
                    # Méthode 1: OCR direct
                    try:
                        text_direct = detector.detect_text_simple(region_img)
                        print(f"   📝 OCR direct: '{text_direct}'")
                    except Exception as e:
                        print(f"   ❌ OCR direct échoué: {e}")
                    
                    # Méthode 2: Avec prétraitement
                    try:
                        # Redimensionner
                        scale_factor = 3
                        resized = cv2.resize(region_img, (width * scale_factor, height * scale_factor))
                        
                        # Convertir en niveaux de gris
                        gray_resized = cv2.cvtColor(resized, cv2.COLOR_BGR2GRAY)
                        
                        # Améliorer le contraste
                        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
                        enhanced = clahe.apply(gray_resized)
                        
                        # Binarisation
                        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                        
                        # Sauvegarder l'image prétraitée
                        preprocessed_filename = f"debug_mise_joueur{i}_preprocessed.jpg"
                        cv2.imwrite(preprocessed_filename, binary)
                        
                        # OCR sur l'image prétraitée
                        text_preprocessed = detector.detect_text_simple(binary)
                        print(f"   📝 OCR prétraité: '{text_preprocessed}'")
                        print(f"   💾 Image prétraitée: {preprocessed_filename}")
                        
                    except Exception as e:
                        print(f"   ❌ OCR prétraité échoué: {e}")
                    
                    # Méthode 3: Détection de montants spécialisée
                    try:
                        text_amount = detector.detect_amount_text(region_img)
                        print(f"   📝 Détection montant: '{text_amount}'")
                    except Exception as e:
                        print(f"   ❌ Détection montant échouée: {e}")
                    
                    # Analyser si la région semble contenir du texte
                    if mean_brightness > 200:
                        print("   ⚠️ Région très claire - peut-être vide")
                    elif mean_brightness < 50:
                        print("   ⚠️ Région très sombre - texte peut être invisible")
                    
                    if contrast < 20:
                        print("   ⚠️ Contraste faible - texte difficile à distinguer")
                    
                    # Vérifier la taille
                    if width < 50 or height < 20:
                        print("   ⚠️ Région très petite - peut causer des problèmes OCR")
                
                else:
                    print(f"\n❌ {region_name}: Région non configurée")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyser_problemes_ocr():
    """Analyse les problèmes OCR courants"""
    print("\n🔍 ANALYSE DES PROBLÈMES OCR COURANTS")
    print("=" * 50)
    
    problemes_courants = [
        {
            "probleme": "Police trop petite",
            "symptomes": ["OCR retourne vide", "Caractères mal reconnus"],
            "solutions": ["Agrandir les régions", "Augmenter le facteur de redimensionnement"]
        },
        {
            "probleme": "Contraste insuffisant",
            "symptomes": ["Texte gris sur fond gris", "Luminosité uniforme"],
            "solutions": ["Améliorer le prétraitement", "Ajuster les seuils de binarisation"]
        },
        {
            "probleme": "Texte partiellement caché",
            "symptomes": ["Montants tronqués", "Caractères coupés"],
            "solutions": ["Ajuster la position des régions", "Agrandir les régions"]
        },
        {
            "probleme": "Couleur de fond variable",
            "symptomes": ["Détection incohérente", "Fonctionne parfois"],
            "solutions": ["Améliorer la détection de couleurs", "Prétraitement adaptatif"]
        },
        {
            "probleme": "Police non standard",
            "symptomes": ["Chiffres mal reconnus", "Confusion entre caractères"],
            "solutions": ["Entraîner OCR spécialisé", "Améliorer les corrections"]
        }
    ]
    
    for i, probleme_info in enumerate(problemes_courants, 1):
        print(f"\n{i}. **{probleme_info['probleme']}**")
        print(f"   Symptômes: {', '.join(probleme_info['symptomes'])}")
        print(f"   Solutions: {', '.join(probleme_info['solutions'])}")

def recommandations_amelioration():
    """Recommandations pour améliorer la détection"""
    print("\n🔧 RECOMMANDATIONS D'AMÉLIORATION")
    print("=" * 50)
    
    print("1. **Vérification des images de debug**")
    print("   - Examinez les fichiers debug_mise_joueur*.jpg")
    print("   - Vérifiez que les régions couvrent bien les montants")
    print("   - Observez la qualité et la lisibilité du texte")
    
    print("\n2. **Ajustement des régions**")
    print("   - Ouvrez calibration_simple.py")
    print("   - Repositionnez les régions problématiques")
    print("   - Agrandissez les régions trop petites")
    print("   - Assurez-vous qu'elles ne se chevauchent pas")
    
    print("\n3. **Amélioration OCR**")
    print("   - Augmentez le facteur de redimensionnement")
    print("   - Ajustez les paramètres de prétraitement")
    print("   - Testez différentes méthodes de binarisation")
    
    print("\n4. **Tests spécifiques**")
    print("   - Testez avec des montants simples (1, 5, 10)")
    print("   - Vérifiez avec différentes tailles de police")
    print("   - Testez sur différentes tables/sites")
    
    print("\n5. **Monitoring en temps réel**")
    print("   - Observez les logs '💲 MISE JOUEUR'")
    print("   - Vérifiez les couleurs détectées")
    print("   - Surveillez les messages d'erreur OCR")

def creer_script_test_specifique():
    """Crée un script pour tester une région spécifique"""
    print("\n🧪 CRÉATION SCRIPT DE TEST SPÉCIFIQUE")
    print("=" * 50)
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test spécifique d'une région de mise
Usage: python test_region_specifique.py <numero_joueur>
"""

import sys
import cv2
import numpy as np
from detector import Detector
import mss
import json

def test_region_specifique(joueur_num):
    """Test une région mise_joueur spécifique"""
    print(f"🎯 TEST RÉGION MISE_JOUEUR{joueur_num}")
    print("=" * 40)
    
    # Créer détecteur
    detector = Detector()
    
    # Capture d'écran
    with mss.mss() as sct:
        screenshot = sct.grab(sct.monitors[1])
        screenshot_np = np.array(screenshot)
        screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_BGRA2BGR)
    
    # Charger config
    config_path = r"C:\\Users\\<USER>\\PokerAdvisor\\Calibration\\config\\poker_advisor_config.json"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    region_name = f"mise_joueur{joueur_num}"
    if region_name not in config['all_regions']:
        print(f"❌ Région {region_name} non trouvée")
        return
    
    region_data = config['all_regions'][region_name]
    x, y, w, h = region_data['x'], region_data['y'], region_data['width'], region_data['height']
    
    # Extraire région
    region_img = screenshot_bgr[y:y+h, x:x+w]
    
    print(f"📍 Position: ({x}, {y}) - Taille: {w}x{h}")
    
    # Tests multiples
    methods = [
        ("OCR Direct", lambda img: detector.detect_text_simple(img)),
        ("Montant Spécialisé", lambda img: detector.detect_amount_text(img)),
        ("Multi-OCR", lambda img: detector.detect_text_multi_ocr(img, False, False))
    ]
    
    for method_name, method_func in methods:
        try:
            result = method_func(region_img)
            print(f"📝 {method_name}: '{result}'")
        except Exception as e:
            print(f"❌ {method_name}: Erreur - {e}")
    
    # Sauvegarder pour inspection
    cv2.imwrite(f"test_mise_joueur{joueur_num}.jpg", region_img)
    print(f"💾 Image sauvée: test_mise_joueur{joueur_num}.jpg")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python test_region_specifique.py <numero_joueur>")
        sys.exit(1)
    
    try:
        joueur_num = int(sys.argv[1])
        test_region_specifique(joueur_num)
    except ValueError:
        print("❌ Numéro de joueur invalide")
'''
    
    with open("test_region_specifique.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print("✅ Script créé: test_region_specifique.py")
    print("🚀 Usage: python test_region_specifique.py 1")
    print("   (remplacez 1 par le numéro du joueur à tester)")

def main():
    """Fonction principale"""
    print("🔍 DIAGNOSTIC - MISES PARTIELLEMENT DÉTECTÉES")
    print("=" * 60)
    print("Analyse pourquoi certaines mises ne sont pas détectées")
    print()
    
    # Analyser les régions
    success = analyser_regions_mises()
    
    # Analyser les problèmes
    analyser_problemes_ocr()
    
    # Recommandations
    recommandations_amelioration()
    
    # Créer script de test
    creer_script_test_specifique()
    
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DU DIAGNOSTIC")
    print("-" * 60)
    
    if success:
        print("✅ Analyse des régions terminée")
        print("📁 Images de debug créées pour inspection")
        print("🔧 Recommandations fournies")
        print("🧪 Script de test spécifique créé")
        
        print("\n🚀 ÉTAPES SUIVANTES :")
        print("1. Examinez les images debug_mise_joueur*.jpg")
        print("2. Identifiez les régions problématiques")
        print("3. Ajustez la calibration si nécessaire")
        print("4. Testez avec: python test_region_specifique.py <numero>")
        print("5. Relancez l'application principale")
    else:
        print("❌ Erreur lors de l'analyse")
        print("Vérifiez que vous êtes sur une table de poker")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
