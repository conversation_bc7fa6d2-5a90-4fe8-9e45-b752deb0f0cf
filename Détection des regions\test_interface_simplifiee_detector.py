#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'interface simplifiée du conseiller poker intégrée dans detector_gui.py
"""

import sys
import os
import time
import json

def create_test_data():
    """Crée des données de test pour l'interface"""
    test_situations = {
        "fold": {
            "card_1": {"text": "A", "colors": ["rouge"]},
            "card_2": {"text": "K", "colors": ["noir"]},
            "card_3": {"text": "Q", "colors": ["rouge"]},
            "carte_1m": {"text": "2", "colors": ["vert"]},
            "carte_2m": {"text": "7", "colors": ["rouge"]},
            "mes_jetons": {"text": "150"},
            "pot_total": {"text": "120"}
        },
        "call": {
            "card_1": {"text": "2", "colors": ["rouge"]},
            "card_2": {"text": "5", "colors": ["vert"]},
            "card_3": {"text": "K", "colors": ["noir"]},
            "carte_1m": {"text": "8", "colors": ["rouge"]},
            "carte_2m": {"text": "8", "colors": ["noir"]},
            "mes_jetons": {"text": "180"},
            "pot_total": {"text": "100"}
        },
        "raise": {
            "card_1": {"text": "2", "colors": ["rouge"]},
            "card_2": {"text": "7", "colors": ["vert"]},
            "card_3": {"text": "9", "colors": ["noir"]},
            "carte_1m": {"text": "A", "colors": ["noir"]},
            "carte_2m": {"text": "A", "colors": ["rouge"]},
            "mes_jetons": {"text": "280"},
            "pot_total": {"text": "80"}
        },
        "allin": {
            "card_1": {"text": "K", "colors": ["noir"]},
            "card_2": {"text": "2", "colors": ["vert"]},
            "card_3": {"text": "7", "colors": ["rouge"]},
            "card_4": {"text": "8", "colors": ["rouge"]},
            "carte_1m": {"text": "K", "colors": ["rouge"]},
            "carte_2m": {"text": "K", "colors": ["rouge"]},
            "mes_jetons": {"text": "80"},
            "pot_total": {"text": "200"}
        }
    }
    return test_situations

def test_interface_simplifiee():
    """Test de l'interface simplifiée avec le conseiller principal"""
    print("🎯 Test de l'interface simplifiée du conseiller poker")
    print("=" * 60)
    
    # Importer l'application principale
    try:
        from detector_gui import DetectorGUI
        from PyQt5.QtWidgets import QApplication
        print("✅ Modules importés avec succès")
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False
    
    # Créer l'application Qt
    app = QApplication(sys.argv)
    
    # Activer le conseiller poker
    os.environ['USE_POKER_ADVISOR'] = '1'
    
    # Créer l'interface
    try:
        detector_gui = DetectorGUI()
        print("✅ Interface DetectorGUI créée")
    except Exception as e:
        print(f"❌ Erreur lors de la création de l'interface: {e}")
        return False
    
    # Vérifier que l'interface simplifiée est présente
    if hasattr(detector_gui, 'action_display'):
        print("✅ Interface simplifiée détectée")
    else:
        print("⚠️ Interface simplifiée non détectée")
    
    if hasattr(detector_gui, 'dealer_position'):
        print("✅ Gestion du bouton dealer détectée")
    else:
        print("⚠️ Gestion du bouton dealer non détectée")
    
    # Afficher l'interface
    detector_gui.show()
    
    # Tester les données
    test_data = create_test_data()
    
    print("\n🎮 Test des différentes situations:")
    print("-" * 40)
    
    for situation, data in test_data.items():
        print(f"\n📋 Test situation: {situation.upper()}")
        
        # Simuler la détection
        if hasattr(detector_gui, 'update_advisor_analysis') and detector_gui.use_advisor:
            try:
                detector_gui.update_advisor_analysis(data)
                print(f"✅ Situation {situation} testée")
            except Exception as e:
                print(f"❌ Erreur pour {situation}: {e}")
        else:
            print(f"⚠️ Conseiller non activé pour {situation}")
        
        # Attendre un peu pour voir le changement
        app.processEvents()
        time.sleep(1)
    
    print("\n🔘 Test de la gestion du bouton dealer:")
    print("-" * 40)
    
    # Tester les boutons de déplacement du dealer
    if hasattr(detector_gui, 'move_dealer_next'):
        for i in range(3):
            detector_gui.move_dealer_next()
            print(f"✅ Bouton déplacé vers position {detector_gui.dealer_position}")
            app.processEvents()
            time.sleep(0.5)
    
    if hasattr(detector_gui, 'move_dealer_previous'):
        for i in range(2):
            detector_gui.move_dealer_previous()
            print(f"✅ Bouton déplacé vers position {detector_gui.dealer_position}")
            app.processEvents()
            time.sleep(0.5)
    
    print("\n✅ Test terminé ! Interface prête à utiliser.")
    print("\n🎯 Fonctionnalités testées :")
    print("   - Interface simplifiée des recommandations")
    print("   - Gestion manuelle du bouton dealer")
    print("   - Positions des joueurs sur table de 6")
    print("   - Intégration avec la logique avancée existante")
    print("   - Conservation de toutes les fonctionnalités")
    
    print("\n🚀 Pour utiliser l'interface complète :")
    print("   1. Lancez 'lancer_detector_cuda_advisor.bat'")
    print("   2. Activez le conseiller poker dans les options")
    print("   3. Utilisez les boutons de déplacement du dealer")
    print("   4. Observez les recommandations simplifiées")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(test_interface_simplifiee())
