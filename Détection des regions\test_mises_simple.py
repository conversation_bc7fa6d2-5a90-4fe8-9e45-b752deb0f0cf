#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple et direct de la détection des mises
"""

import sys
import os
import json

def test_configuration_mises():
    """Test de la configuration des mises"""
    print("🔧 TEST CONFIGURATION DES MISES")
    print("=" * 40)
    
    config_path = r"C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json"
    
    if not os.path.exists(config_path):
        print("❌ Configuration non trouvée !")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        all_regions = config.get('all_regions', {})
        
        # Compter les régions de mises
        mise_regions = [name for name in all_regions.keys() if 'mise_joueur' in name]
        
        print(f"📊 Régions mise_joueur trouvées: {len(mise_regions)}")
        
        if len(mise_regions) == 0:
            print("❌ AUCUNE RÉGION MISE_JOUEUR !")
            print("   Vous devez calibrer les régions mise_joueur1 à mise_joueur6")
            return False
        
        for region_name in sorted(mise_regions):
            region_data = all_regions[region_name]
            x = region_data.get('x', 0)
            y = region_data.get('y', 0)
            width = region_data.get('width', 0)
            height = region_data.get('height', 0)
            
            print(f"✅ {region_name}: ({x}, {y}) - {width}x{height}")
            
            if width < 20 or height < 20:
                print(f"   ⚠️ Région très petite !")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_detection_directe():
    """Test de détection directe avec capture d'écran"""
    print("\n📸 TEST DÉTECTION DIRECTE")
    print("=" * 40)
    
    try:
        # Importer le détecteur
        from detector import Detector
        
        # Créer le détecteur
        detector = Detector()
        print("✅ Détecteur créé")
        
        # Faire une capture d'écran
        print("📸 Capture d'écran...")
        
        # Utiliser mss pour capturer
        import mss
        with mss.mss() as sct:
            # Capturer tout l'écran
            screenshot = sct.grab(sct.monitors[1])
            
            # Convertir en numpy array
            import numpy as np
            screenshot_np = np.array(screenshot)
            
            # Convertir BGRA vers BGR
            import cv2
            screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_BGRA2BGR)
            
            print(f"✅ Capture réussie: {screenshot_bgr.shape}")
            
            # Traiter l'image directement
            print("🔍 Traitement de l'image...")
            results = detector.process_image_direct(screenshot_bgr, fast_mode=True)
            
            print(f"✅ {len(results)} régions traitées")
            
            # Chercher les mises
            mises_detectees = {}
            for region_name, region_data in results.items():
                if 'mise_joueur' in region_name:
                    text = region_data.get('text', '').strip()
                    colors = region_data.get('colors', [])
                    
                    if text:
                        mises_detectees[region_name] = {
                            'text': text,
                            'colors': colors
                        }
                        print(f"💲 {region_name}: '{text}' - {colors}")
                    else:
                        print(f"⚪ {region_name}: Vide")
            
            if mises_detectees:
                print(f"\n✅ {len(mises_detectees)} mises détectées !")
                return True
            else:
                print("\n❌ Aucune mise détectée")
                return False
                
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_logs_console():
    """Test des logs dans la console"""
    print("\n📝 TEST LOGS CONSOLE")
    print("=" * 40)
    
    print("🔍 Messages à chercher dans la console :")
    print("   💲 MISE JOUEUR - Région 'mise_joueur1': '...'")
    print("   ✅ Région mise_joueur1: '...' - [...]")
    print("   🔍 Détection de montant appliquée à la région 'mise_joueur1'")
    
    print("\n🚀 Pour voir les logs en temps réel :")
    print("   1. Lancez 'lancer_detector_cuda_advisor.bat'")
    print("   2. Activez le conseiller poker")
    print("   3. Observez la console")
    
    return True

def analyser_probleme():
    """Analyse le problème et donne des solutions"""
    print("\n🔍 ANALYSE DU PROBLÈME")
    print("=" * 40)
    
    # Vérifier la configuration
    config_ok = test_configuration_mises()
    
    if not config_ok:
        print("\n🚨 PROBLÈME IDENTIFIÉ: CONFIGURATION")
        print("-" * 40)
        print("❌ Les régions mise_joueur ne sont pas calibrées")
        print()
        print("🔧 SOLUTION :")
        print("1. Ouvrez calibration_simple.py")
        print("2. Ajoutez les régions mise_joueur1 à mise_joueur6")
        print("3. Placez-les exactement sur les montants des mises")
        print("4. Sauvegardez la configuration")
        return False
    
    # Tester la détection
    detection_ok = test_detection_directe()
    
    if not detection_ok:
        print("\n🚨 PROBLÈME IDENTIFIÉ: DÉTECTION")
        print("-" * 40)
        print("❌ Les régions sont calibrées mais ne détectent rien")
        print()
        print("🔧 SOLUTIONS POSSIBLES :")
        print("1. **Régions mal placées**")
        print("   - Vérifiez que les régions couvrent bien les montants")
        print("   - Ajustez la taille et position dans calibration_simple.py")
        print()
        print("2. **OCR ne lit pas les montants**")
        print("   - Police trop petite ou illisible")
        print("   - Contraste insuffisant")
        print("   - Montants cachés ou tronqués")
        print()
        print("3. **Pas de mises actuellement**")
        print("   - Testez avec une table où il y a des mises")
        print("   - Vérifiez que vous êtes sur une table de poker")
        return False
    
    print("\n✅ DÉTECTION FONCTIONNE !")
    print("-" * 40)
    print("Les mises sont détectées correctement.")
    print("Si elles n'apparaissent pas dans le conseiller,")
    print("le problème est dans l'intégration.")
    
    return True

def main():
    """Fonction principale"""
    print("🎯 TEST SIMPLE - DÉTECTION DES MISES")
    print("=" * 50)
    print("Diagnostic rapide du problème de détection des mises")
    print()
    
    # Analyser le problème
    resultat = analyser_probleme()
    
    # Logs console
    test_logs_console()
    
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ")
    print("-" * 50)
    
    if resultat:
        print("✅ La détection des mises fonctionne")
        print("🔍 Si le problème persiste, vérifiez l'intégration")
        print("   dans poker_advisor_light.py et detector_gui.py")
    else:
        print("❌ Problème identifié avec solutions proposées")
    
    print("\n🚀 ÉTAPES SUIVANTES :")
    print("-" * 50)
    print("1. Corrigez le problème identifié")
    print("2. Relancez ce test")
    print("3. Testez avec l'application complète")
    print("4. Observez les logs en temps réel")
    
    return resultat

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
